# MathCapture Studio - Responsive Edition

A modern, responsive Windows desktop application for extracting mathematical equations from PDFs and images using OCR technology, with export capabilities to Microsoft Word documents.

![MathCapture Studio Responsive](assets/screenshots/responsive_interface.png)

## 🆕 What's New in Responsive Edition (v2.0.0)

### 🎨 **Modern Responsive Design**
- **Adaptive Layout**: Automatically adjusts to different screen sizes and resolutions
- **Three Layout Modes**: Desktop, Tablet, and Mobile-optimized interfaces
- **Collapsible Panels**: Hide/show sidebar and bottom panels for optimal screen usage
- **Modern Styling**: Clean, professional interface with modern color scheme
- **High DPI Support**: Crisp display on high-resolution monitors

### 📱 **Responsive Features**
- **Smart Panel Management**: Panels automatically resize and reposition based on window size
- **Touch-Friendly Controls**: Larger buttons and touch-optimized interactions
- **Keyboard Shortcuts**: Comprehensive keyboard navigation support
- **Fullscreen Mode**: Distraction-free editing with F11 toggle
- **Zoom Controls**: Mouse wheel zoom and responsive zoom-to-fit

### 🔧 **Enhanced User Experience**
- **Modern Tooltips**: Helpful hints for all interface elements
- **Progress Indicators**: Real-time feedback for all operations
- **Status Bar**: Comprehensive information display
- **Quick Access Toolbar**: Essential tools always within reach
- **Responsive Navigation**: Smooth page navigation with jump-to-page functionality

## Features

### 🔍 **Advanced OCR Processing**
- Extract mathematical equations from PDFs and images
- Support for multiple image formats (PNG, JPG, JPEG, BMP, TIFF)
- Multi-page PDF processing with responsive preview
- Tesseract OCR with math-trained models
- Automatic symbol recognition and conversion
- Confidence scoring and validation

### ✏️ **Equation Editing**
- Real-time LaTeX editing with syntax highlighting
- Live preview of mathematical expressions
- Auto-correction of common OCR errors
- Symbol replacement and formatting
- Responsive editor that adapts to screen size

### 📋 **Queue Management**
- Organize extracted equations in a responsive queue
- Drag-and-drop reordering
- Edit equations inline
- Batch processing capabilities
- Smart queue statistics

### 📄 **Word Export**
- Export to Microsoft Word (.docx) format
- OMML (Office Math Markup Language) support
- Maintain mathematical formatting
- Include source references and page numbers
- Customizable export options

### 💾 **Project Management**
- Save and load project sessions
- Preserve equation queue and settings
- Resume work on complex documents
- Auto-save functionality

## Installation

### Prerequisites

1. **Python 3.8 or higher**
   - Download from [python.org](https://www.python.org/downloads/)

2. **Tesseract OCR**
   - Download from [UB-Mannheim Tesseract](https://github.com/UB-Mannheim/tesseract/wiki)
   - Make sure `tesseract.exe` is in your system PATH
   - Install with math training data for better equation recognition

### Quick Install

1. **Extract the project files**
   \`\`\`bash
   unzip MathCapture_Studio_Responsive_v2.0.zip
   cd MathCapture_Studio
   \`\`\`

2. **Install Python dependencies**
   \`\`\`bash
   pip install -r requirements.txt
   \`\`\`

3. **Run the application**
   \`\`\`bash
   python main.py
   \`\`\`

## Quick Start Guide

### 1. Import Files
- Click "📁 Import" or use `Ctrl+O`
- Select PDF files or images containing mathematical equations
- Files will appear in the responsive sidebar with page thumbnails

### 2. Navigate and View
- Use navigation controls or keyboard shortcuts
- Zoom with mouse wheel or toolbar controls
- Toggle fullscreen with `F11` for distraction-free viewing

### 3. Select Equation Regions
- Click and drag to select regions containing equations
- Use responsive zoom controls for precise selection
- Double-click for auto-detection (coming soon)

### 4. Process with OCR
- Click "🔍 Process" to extract text from selected regions
- Review the OCR result in the responsive editor panel
- Edit the LaTeX code as needed with live preview

### 5. Build Equation Queue
- Click "➕ Add" to add equations to the queue
- Reorder equations using the up/down buttons
- Edit equations by double-clicking in the queue

### 6. Export to Word
- Click "📄 Export" or use `Ctrl+E`
- Choose output location and filename
- Open the generated Word document

## Responsive Design

### Layout Modes

#### 🖥️ Desktop Mode (>1300px width)
- Full three-panel layout
- Sidebar, main preview, and editor all visible
- Maximum productivity with all tools accessible

#### 📱 Tablet Mode (1000-1300px width)
- Optimized two-panel layout
- Collapsible sidebar for more preview space
- Touch-friendly controls

#### 📱 Mobile Mode (<1000px width)
- Single-panel focus mode
- Collapsible panels for maximum content area
- Simplified navigation

### Adaptive Features

- **Smart Panel Sizing**: Panels automatically adjust to optimal sizes
- **Responsive Typography**: Text scales appropriately for readability
- **Touch Optimization**: Larger touch targets on smaller screens
- **Keyboard Navigation**: Full keyboard support for all functions

## Keyboard Shortcuts

| Shortcut | Action |
|----------|--------|
| `Ctrl+O` | Import files |
| `Ctrl+N` | New project |
| `Ctrl+S` | Save project |
| `Ctrl+L` | Load project |
| `Ctrl+E` | Export to Word |
| `Ctrl+B` | Toggle sidebar |
| `Ctrl+J` | Toggle bottom panel |
| `Ctrl++` | Zoom in |
| `Ctrl+-` | Zoom out |
| `Ctrl+0` | Fit to window |
| `F11` | Toggle fullscreen |
| `Esc` | Exit fullscreen |
| `Ctrl+,` | Open settings |

## Configuration

### Responsive Settings
The application automatically saves responsive layout preferences:

\`\`\`json
{
  "responsive_breakpoints": {
    "mobile": 1000,
    "tablet": 1300,
    "desktop": 1300
  },
  "adaptive_ui": true,
  "high_dpi_scaling": true,
  "remember_window_state": true,
  "sidebar_width": 300,
  "editor_width": 350,
  "queue_height": 200
}
\`\`\`

### UI Customization
- **Theme**: Modern light theme with professional colors
- **Scaling**: Automatic high-DPI scaling support
- **Layout**: Persistent panel sizes and positions
- **Accessibility**: High contrast mode and keyboard navigation

## System Requirements

### Minimum Requirements
- **OS**: Windows 10 or later
- **RAM**: 4GB minimum, 8GB recommended
- **Storage**: 500MB free space
- **Display**: 1024x768 minimum resolution

### Recommended Requirements
- **OS**: Windows 11
- **RAM**: 8GB or more
- **Storage**: 1GB free space
- **Display**: 1920x1080 or higher resolution
- **Input**: Mouse and keyboard (touch support optional)

## Troubleshooting

### Common Issues

#### Layout Problems
- **Issue**: Panels not displaying correctly
- **Solution**: Reset window state in settings or restart application

#### High DPI Issues
- **Issue**: Blurry text on high-resolution displays
- **Solution**: Enable high DPI scaling in settings

#### Performance Issues
- **Issue**: Slow response on large files
- **Solution**: Reduce image quality or process in smaller batches

### Getting Help

1. **User Manual**: See built-in help system (`F1`)
2. **Keyboard Shortcuts**: Press `Ctrl+?` for quick reference
3. **Settings Reset**: Use "Reset to Defaults" in settings
4. **Debug Mode**: Enable in settings for detailed logging

## Development

### Project Structure
\`\`\`
MathCapture_Studio/
├── main.py                 # Main responsive application
├── requirements.txt        # Python dependencies
├── components/             # Core modules
│   ├── __init__.py
│   ├── ocr_processor.py    # OCR processing logic
│   ├── word_exporter.py    # Word document export
│   ├── image_processor.py  # Image preprocessing
│   └── settings_manager.py # Responsive settings management
├── assets/                 # Icons and resources
├── docs/                   # Documentation
└── tests/                  # Unit tests
\`\`\`

### Contributing
1. Fork the repository
2. Create a feature branch
3. Implement responsive design principles
4. Test on multiple screen sizes
5. Submit a pull request

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Acknowledgments

- **Tesseract OCR** team for the excellent OCR engine
- **python-docx** developers for Word document support
- **OpenCV** community for image processing capabilities
- **Tkinter** for the responsive GUI framework

## Changelog

### v2.0.0 - Responsive Edition
- ✨ Complete responsive design overhaul
- 🎨 Modern UI with adaptive layouts
- 📱 Support for multiple screen sizes
- ⌨️ Comprehensive keyboard shortcuts
- 🖥️ High DPI display support
- 🔧 Enhanced settings management
- 📊 Real-time progress indicators
- 🎯 Improved user experience

### v1.0.0 - Initial Release
- Basic OCR functionality
- Word export capabilities
- Project management
- Simple UI layout

---

**MathCapture Studio - Responsive Edition** - Making mathematical content extraction beautiful and efficient on any screen size.
