"""
Performance Monitoring System for MathCapture Studio
Tracks and analyzes OCR engine performance with detailed metrics and reporting.
"""

import time
import json
import os
import threading
from typing import Dict, Any, List, Optional
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
import logging

@dataclass
class ProcessingEvent:
    """Individual processing event record"""
    timestamp: float
    engine: str
    processing_time: float
    confidence: float
    success: bool
    image_size: tuple
    complexity_score: float
    text_length: int
    error_message: str = ""

@dataclass
class EngineStats:
    """Statistics for a specific OCR engine"""
    total_processed: int = 0
    total_time: float = 0.0
    total_success: int = 0
    average_time: float = 0.0
    average_confidence: float = 0.0
    success_rate: float = 0.0
    last_used: float = 0.0
    error_count: int = 0
    
    def update(self, event: ProcessingEvent):
        """Update stats with new processing event"""
        self.total_processed += 1
        self.total_time += event.processing_time
        self.average_time = self.total_time / self.total_processed
        
        if event.success:
            self.total_success += 1
            # Update rolling average confidence for successful operations
            alpha = 0.1
            self.average_confidence = (alpha * event.confidence + 
                                     (1 - alpha) * self.average_confidence)
        else:
            self.error_count += 1
            
        self.success_rate = self.total_success / self.total_processed
        self.last_used = event.timestamp

class PerformanceMonitor:
    """Comprehensive performance monitoring system"""
    
    def __init__(self, settings: Dict[str, Any]):
        self.settings = settings
        self.logger = logging.getLogger(__name__)
        
        # Event storage
        self.events: List[ProcessingEvent] = []
        self.max_events = settings.get('max_performance_events', 1000)
        
        # Engine statistics
        self.engine_stats: Dict[str, EngineStats] = {
            'tesseract': EngineStats(),
            'donut': EngineStats(),
            'hybrid': EngineStats()
        }
        
        # Session tracking
        self.session_start = time.time()
        self.session_stats = {
            'total_processed': 0,
            'total_time': 0.0,
            'engines_used': {'tesseract': 0, 'donut': 0, 'hybrid': 0},
            'complexity_distribution': {'low': 0, 'medium': 0, 'high': 0},
            'confidence_distribution': {'low': 0, 'medium': 0, 'high': 0}
        }
        
        # Performance thresholds
        self.thresholds = {
            'slow_processing_time': settings.get('slow_processing_threshold', 10.0),
            'low_confidence': settings.get('low_confidence_threshold', 50.0),
            'high_confidence': settings.get('high_confidence_threshold', 80.0)
        }
        
        # Thread safety
        self.lock = threading.Lock()
        
        # Auto-save settings
        self.auto_save_enabled = settings.get('auto_save_performance_data', True)
        self.save_interval = settings.get('performance_save_interval', 300)  # 5 minutes
        self.last_save = time.time()
        
        self.logger.info("Performance monitor initialized")
    
    def record_event(self, engine: str, processing_time: float, confidence: float,
                    success: bool, image_size: tuple, complexity_score: float = 0.0,
                    text_length: int = 0, error_message: str = ""):
        """Record a processing event"""
        with self.lock:
            event = ProcessingEvent(
                timestamp=time.time(),
                engine=engine,
                processing_time=processing_time,
                confidence=confidence,
                success=success,
                image_size=image_size,
                complexity_score=complexity_score,
                text_length=text_length,
                error_message=error_message
            )
            
            # Add to events list
            self.events.append(event)
            
            # Maintain max events limit
            if len(self.events) > self.max_events:
                self.events.pop(0)
            
            # Update engine stats
            if engine in self.engine_stats:
                self.engine_stats[engine].update(event)
            
            # Update session stats
            self._update_session_stats(event)
            
            # Check for auto-save
            if (self.auto_save_enabled and 
                time.time() - self.last_save > self.save_interval):
                self._auto_save_data()
            
            self.logger.debug(f"Recorded {engine} event: {processing_time:.2f}s, "
                            f"{confidence:.1f}% confidence, success: {success}")
    
    def _update_session_stats(self, event: ProcessingEvent):
        """Update session statistics"""
        self.session_stats['total_processed'] += 1
        self.session_stats['total_time'] += event.processing_time
        
        # Update engine usage
        if event.engine in self.session_stats['engines_used']:
            self.session_stats['engines_used'][event.engine] += 1
        
        # Update complexity distribution
        if event.complexity_score < 30:
            self.session_stats['complexity_distribution']['low'] += 1
        elif event.complexity_score < 70:
            self.session_stats['complexity_distribution']['medium'] += 1
        else:
            self.session_stats['complexity_distribution']['high'] += 1
        
        # Update confidence distribution
        if event.confidence < self.thresholds['low_confidence']:
            self.session_stats['confidence_distribution']['low'] += 1
        elif event.confidence < self.thresholds['high_confidence']:
            self.session_stats['confidence_distribution']['medium'] += 1
        else:
            self.session_stats['confidence_distribution']['high'] += 1
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """Get comprehensive performance summary"""
        with self.lock:
            summary = {
                'session_info': {
                    'start_time': self.session_start,
                    'duration': time.time() - self.session_start,
                    'total_events': len(self.events)
                },
                'session_stats': self.session_stats.copy(),
                'engine_stats': {name: asdict(stats) for name, stats in self.engine_stats.items()},
                'recent_performance': self._get_recent_performance(),
                'performance_trends': self._analyze_trends(),
                'recommendations': self._generate_recommendations()
            }
            
            return summary
    
    def _get_recent_performance(self, minutes: int = 10) -> Dict[str, Any]:
        """Get performance data for recent time period"""
        cutoff_time = time.time() - (minutes * 60)
        recent_events = [e for e in self.events if e.timestamp > cutoff_time]
        
        if not recent_events:
            return {'period_minutes': minutes, 'events': 0}
        
        # Calculate recent metrics
        total_time = sum(e.processing_time for e in recent_events)
        successful_events = [e for e in recent_events if e.success]
        
        avg_time = total_time / len(recent_events) if recent_events else 0
        avg_confidence = (sum(e.confidence for e in successful_events) / 
                         len(successful_events) if successful_events else 0)
        success_rate = len(successful_events) / len(recent_events) if recent_events else 0
        
        # Engine usage in recent period
        engine_usage = {}
        for event in recent_events:
            engine_usage[event.engine] = engine_usage.get(event.engine, 0) + 1
        
        return {
            'period_minutes': minutes,
            'events': len(recent_events),
            'average_time': avg_time,
            'average_confidence': avg_confidence,
            'success_rate': success_rate,
            'engine_usage': engine_usage
        }
    
    def _analyze_trends(self) -> Dict[str, Any]:
        """Analyze performance trends over time"""
        if len(self.events) < 10:
            return {'insufficient_data': True}
        
        # Split events into time buckets for trend analysis
        now = time.time()
        hour_ago = now - 3600
        
        recent_events = [e for e in self.events if e.timestamp > hour_ago]
        if len(recent_events) < 5:
            return {'insufficient_recent_data': True}
        
        # Calculate trends
        time_buckets = {}
        bucket_size = 600  # 10-minute buckets
        
        for event in recent_events:
            bucket = int((event.timestamp - hour_ago) // bucket_size)
            if bucket not in time_buckets:
                time_buckets[bucket] = []
            time_buckets[bucket].append(event)
        
        # Analyze each bucket
        bucket_stats = []
        for bucket_id in sorted(time_buckets.keys()):
            events = time_buckets[bucket_id]
            successful = [e for e in events if e.success]
            
            bucket_stats.append({
                'bucket_id': bucket_id,
                'event_count': len(events),
                'avg_time': sum(e.processing_time for e in events) / len(events),
                'avg_confidence': (sum(e.confidence for e in successful) / len(successful) 
                                 if successful else 0),
                'success_rate': len(successful) / len(events)
            })
        
        # Calculate trends
        trends = {}
        if len(bucket_stats) >= 2:
            first_half = bucket_stats[:len(bucket_stats)//2]
            second_half = bucket_stats[len(bucket_stats)//2:]
            
            avg_time_first = sum(b['avg_time'] for b in first_half) / len(first_half)
            avg_time_second = sum(b['avg_time'] for b in second_half) / len(second_half)
            
            avg_conf_first = sum(b['avg_confidence'] for b in first_half) / len(first_half)
            avg_conf_second = sum(b['avg_confidence'] for b in second_half) / len(second_half)
            
            trends = {
                'processing_time_trend': 'improving' if avg_time_second < avg_time_first else 'degrading',
                'confidence_trend': 'improving' if avg_conf_second > avg_conf_first else 'degrading',
                'time_change_percent': ((avg_time_second - avg_time_first) / avg_time_first * 100 
                                       if avg_time_first > 0 else 0),
                'confidence_change_percent': ((avg_conf_second - avg_conf_first) / avg_conf_first * 100 
                                            if avg_conf_first > 0 else 0)
            }
        
        return {
            'bucket_stats': bucket_stats,
            'trends': trends
        }
    
    def _generate_recommendations(self) -> List[Dict[str, str]]:
        """Generate performance recommendations"""
        recommendations = []
        
        # Analyze engine performance
        tesseract_stats = self.engine_stats['tesseract']
        donut_stats = self.engine_stats['donut']
        
        # Recommendation based on success rates
        if tesseract_stats.total_processed > 10 and tesseract_stats.success_rate < 0.7:
            recommendations.append({
                'type': 'engine_performance',
                'priority': 'high',
                'message': 'Tesseract success rate is low. Consider using Donut for complex equations.',
                'action': 'Switch to Donut or Hybrid mode for better accuracy'
            })
        
        if donut_stats.total_processed > 5 and donut_stats.average_time > 15:
            recommendations.append({
                'type': 'performance',
                'priority': 'medium',
                'message': 'Donut processing is slow. Consider using GPU acceleration.',
                'action': 'Enable GPU processing in settings or use Hybrid mode'
            })
        
        # Recommendation based on confidence
        recent_low_confidence = len([e for e in self.events[-20:] 
                                   if e.confidence < self.thresholds['low_confidence']])
        if recent_low_confidence > 10:
            recommendations.append({
                'type': 'accuracy',
                'priority': 'high',
                'message': 'Many recent results have low confidence.',
                'action': 'Try preprocessing images or switching to Donut engine'
            })
        
        # Recommendation based on processing time
        recent_slow = len([e for e in self.events[-20:] 
                          if e.processing_time > self.thresholds['slow_processing_time']])
        if recent_slow > 5:
            recommendations.append({
                'type': 'performance',
                'priority': 'medium',
                'message': 'Processing times are consistently slow.',
                'action': 'Consider reducing image size or enabling result caching'
            })
        
        return recommendations
    
    def get_engine_comparison(self) -> Dict[str, Any]:
        """Compare performance between different engines"""
        comparison = {}
        
        for engine_name, stats in self.engine_stats.items():
            if stats.total_processed > 0:
                comparison[engine_name] = {
                    'total_processed': stats.total_processed,
                    'average_time': stats.average_time,
                    'average_confidence': stats.average_confidence,
                    'success_rate': stats.success_rate,
                    'error_rate': stats.error_count / stats.total_processed,
                    'last_used': stats.last_used
                }
        
        # Calculate relative performance
        if len(comparison) > 1:
            engines = list(comparison.keys())
            for i, engine1 in enumerate(engines):
                for engine2 in engines[i+1:]:
                    stats1 = comparison[engine1]
                    stats2 = comparison[engine2]
                    
                    comparison[f'{engine1}_vs_{engine2}'] = {
                        'speed_ratio': stats2['average_time'] / stats1['average_time'] if stats1['average_time'] > 0 else 0,
                        'confidence_diff': stats1['average_confidence'] - stats2['average_confidence'],
                        'success_rate_diff': stats1['success_rate'] - stats2['success_rate']
                    }
        
        return comparison
    
    def export_performance_data(self, filepath: str) -> bool:
        """Export performance data to file"""
        try:
            with self.lock:
                data = {
                    'export_timestamp': time.time(),
                    'session_info': {
                        'start_time': self.session_start,
                        'duration': time.time() - self.session_start
                    },
                    'events': [asdict(event) for event in self.events],
                    'engine_stats': {name: asdict(stats) for name, stats in self.engine_stats.items()},
                    'session_stats': self.session_stats,
                    'settings': self.settings
                }
                
                with open(filepath, 'w', encoding='utf-8') as f:
                    json.dump(data, f, indent=2, ensure_ascii=False)
                
                self.logger.info(f"Performance data exported to {filepath}")
                return True
                
        except Exception as e:
            self.logger.error(f"Failed to export performance data: {e}")
            return False
    
    def _auto_save_data(self):
        """Auto-save performance data"""
        try:
            config_dir = os.path.join(os.path.expanduser("~"), ".mathcapture_studio")
            os.makedirs(config_dir, exist_ok=True)
            
            filepath = os.path.join(config_dir, "performance_data.json")
            if self.export_performance_data(filepath):
                self.last_save = time.time()
                self.logger.debug("Performance data auto-saved")
        except Exception as e:
            self.logger.error(f"Auto-save failed: {e}")
    
    def clear_data(self):
        """Clear all performance data"""
        with self.lock:
            self.events.clear()
            self.engine_stats = {
                'tesseract': EngineStats(),
                'donut': EngineStats(),
                'hybrid': EngineStats()
            }
            self.session_stats = {
                'total_processed': 0,
                'total_time': 0.0,
                'engines_used': {'tesseract': 0, 'donut': 0, 'hybrid': 0},
                'complexity_distribution': {'low': 0, 'medium': 0, 'high': 0},
                'confidence_distribution': {'low': 0, 'medium': 0, 'high': 0}
            }
            self.session_start = time.time()
            
        self.logger.info("Performance data cleared")
    
    def get_real_time_stats(self) -> Dict[str, Any]:
        """Get real-time performance statistics"""
        with self.lock:
            recent_events = self.events[-10:] if self.events else []
            
            return {
                'current_session_duration': time.time() - self.session_start,
                'total_events': len(self.events),
                'recent_events': len(recent_events),
                'recent_average_time': (sum(e.processing_time for e in recent_events) / 
                                      len(recent_events) if recent_events else 0),
                'recent_average_confidence': (sum(e.confidence for e in recent_events if e.success) / 
                                            len([e for e in recent_events if e.success]) 
                                            if any(e.success for e in recent_events) else 0),
                'engines_status': {
                    name: {
                        'last_used': stats.last_used,
                        'recent_usage': len([e for e in recent_events if e.engine == name])
                    }
                    for name, stats in self.engine_stats.items()
                }
            }
