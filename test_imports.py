#!/usr/bin/env python3
"""
Test script to verify all imports and basic functionality
"""

import sys
import os

def test_imports():
    """Test all module imports"""
    print("Testing imports...")
    
    try:
        # Test component imports
        sys.path.append(os.path.join(os.path.dirname(__file__), 'components'))
        
        from components.ocr_processor import MathOCRProcessor, OCREngine, OCRResult
        print("✓ OCR Processor imported successfully")
        
        from components.word_exporter import WordExporter
        print("✓ Word Exporter imported successfully")
        
        from components.image_processor import ImageProcessor
        print("✓ Image Processor imported successfully")
        
        from components.settings_manager import SettingsManager
        print("✓ Settings Manager imported successfully")
        
        from components.performance_monitor import PerformanceMonitor
        print("✓ Performance Monitor imported successfully")
        
        # Test main application import
        import main
        print("✓ Main application imported successfully")
        
        return True
        
    except ImportError as e:
        print(f"✗ Import failed: {e}")
        return False
    except Exception as e:
        print(f"✗ Unexpected error: {e}")
        return False

def test_basic_functionality():
    """Test basic functionality without GUI"""
    print("\nTesting basic functionality...")
    
    try:
        # Test WordExporter
        from components.word_exporter import WordExporter, ExportSettings
        exporter = WordExporter()
        settings = ExportSettings()
        print("✓ WordExporter can be instantiated")
        
        # Test ImageProcessor
        from components.image_processor import ImageProcessor, ProcessingSettings
        processor = ImageProcessor()
        proc_settings = ProcessingSettings()
        print("✓ ImageProcessor can be instantiated")
        
        # Test supported formats
        formats = exporter.get_supported_formats()
        print(f"✓ WordExporter supports formats: {formats}")
        
        return True
        
    except Exception as e:
        print(f"✗ Functionality test failed: {e}")
        return False

def test_dependencies():
    """Test critical dependencies"""
    print("\nTesting dependencies...")
    
    dependencies = [
        ('tkinter', 'GUI framework'),
        ('PIL', 'Image processing'),
        ('cv2', 'Computer vision'),
        ('numpy', 'Numerical computing'),
        ('docx', 'Word document generation'),
        ('pytesseract', 'OCR engine'),
        ('fitz', 'PDF processing'),
        ('torch', 'Deep learning framework'),
        ('transformers', 'AI models')
    ]
    
    missing = []
    available = []
    
    for dep, description in dependencies:
        try:
            __import__(dep)
            available.append((dep, description))
            print(f"✓ {dep} - {description}")
        except ImportError:
            missing.append((dep, description))
            print(f"✗ {dep} - {description} (missing)")
    
    print(f"\nSummary: {len(available)} available, {len(missing)} missing")
    
    if missing:
        print("\nMissing dependencies:")
        for dep, desc in missing:
            print(f"  - {dep}: {desc}")
    
    return len(missing) == 0

if __name__ == "__main__":
    print("MathCapture Studio - Dependency and Import Test")
    print("=" * 50)
    
    success = True
    
    # Run tests
    success &= test_dependencies()
    success &= test_imports()
    success &= test_basic_functionality()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 All tests passed! The application should work correctly.")
    else:
        print("⚠️  Some tests failed. Check the output above for details.")
    
    print("\nTo run the application:")
    print("  python main.py")
