#!/usr/bin/env python3
"""
Word Exporter Module for MathCapture Studio
Handles exporting mathematical equations and content to Microsoft Word documents
"""

import os
import logging
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
from docx import Document
from docx.shared import Inches, Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.enum.style import WD_STYLE_TYPE
from docx.oxml.shared import OxmlElement, qn
import tempfile
import base64
from PIL import Image
import io

@dataclass
class ExportSettings:
    """Settings for Word export"""
    include_images: bool = True
    include_latex: bool = True
    include_confidence: bool = False
    page_margins: float = 1.0  # inches
    font_size: int = 12
    equation_font_size: int = 14
    title: str = "Mathematical Equations Export"
    author: str = "MathCapture Studio"

class WordExporter:
    """Handles exporting mathematical equations to Word documents"""
    
    def __init__(self, settings: Dict[str, Any] = None):
        self.settings = settings or {}
        self.logger = logging.getLogger(__name__)
        
        # Default export settings
        self.export_settings = ExportSettings()
        
    def export_equations(self, equations: List[Dict[str, Any]], output_path: str, 
                        export_settings: ExportSettings = None) -> bool:
        """
        Export equations to a Word document
        
        Args:
            equations: List of equation data dictionaries
            output_path: Path to save the Word document
            export_settings: Export configuration settings
            
        Returns:
            bool: True if export successful, False otherwise
        """
        try:
            if export_settings:
                self.export_settings = export_settings
                
            # Create new document
            doc = Document()
            
            # Set up document properties
            self._setup_document_properties(doc)
            
            # Set up styles
            self._setup_document_styles(doc)
            
            # Add title
            self._add_title(doc)
            
            # Add equations
            for i, equation in enumerate(equations, 1):
                self._add_equation_section(doc, equation, i)
                
            # Save document
            doc.save(output_path)
            
            self.logger.info(f"Successfully exported {len(equations)} equations to {output_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to export equations: {e}")
            return False
    
    def _setup_document_properties(self, doc: Document):
        """Setup document properties"""
        try:
            core_props = doc.core_properties
            core_props.title = self.export_settings.title
            core_props.author = self.export_settings.author
            core_props.subject = "Mathematical Equations"
            core_props.comments = "Generated by MathCapture Studio"
            
            # Set page margins
            sections = doc.sections
            for section in sections:
                section.top_margin = Inches(self.export_settings.page_margins)
                section.bottom_margin = Inches(self.export_settings.page_margins)
                section.left_margin = Inches(self.export_settings.page_margins)
                section.right_margin = Inches(self.export_settings.page_margins)
                
        except Exception as e:
            self.logger.warning(f"Could not set document properties: {e}")
    
    def _setup_document_styles(self, doc: Document):
        """Setup custom styles for the document"""
        try:
            styles = doc.styles
            
            # Create equation style
            if 'Equation' not in [style.name for style in styles]:
                equation_style = styles.add_style('Equation', WD_STYLE_TYPE.PARAGRAPH)
                equation_style.font.name = 'Cambria Math'
                equation_style.font.size = Pt(self.export_settings.equation_font_size)
                equation_style.paragraph_format.alignment = WD_ALIGN_PARAGRAPH.CENTER
                equation_style.paragraph_format.space_after = Pt(6)
                equation_style.paragraph_format.space_before = Pt(6)
            
            # Create equation caption style
            if 'Equation Caption' not in [style.name for style in styles]:
                caption_style = styles.add_style('Equation Caption', WD_STYLE_TYPE.PARAGRAPH)
                caption_style.font.name = 'Calibri'
                caption_style.font.size = Pt(10)
                caption_style.font.italic = True
                caption_style.paragraph_format.alignment = WD_ALIGN_PARAGRAPH.CENTER
                caption_style.paragraph_format.space_after = Pt(12)
                
        except Exception as e:
            self.logger.warning(f"Could not setup custom styles: {e}")
    
    def _add_title(self, doc: Document):
        """Add title to the document"""
        title = doc.add_heading(self.export_settings.title, 0)
        title.alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        # Add subtitle with timestamp
        import datetime
        subtitle = doc.add_paragraph(f"Generated on {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        subtitle.alignment = WD_ALIGN_PARAGRAPH.CENTER
        subtitle_format = subtitle.runs[0].font
        subtitle_format.size = Pt(10)
        subtitle_format.italic = True
        
        # Add spacing
        doc.add_paragraph()
    
    def _add_equation_section(self, doc: Document, equation: Dict[str, Any], equation_number: int):
        """Add an equation section to the document"""
        try:
            # Add equation header
            header = doc.add_heading(f"Equation {equation_number}", level=2)
            
            # Add source information if available
            if equation.get('filename') and equation.get('page_num') is not None:
                source_para = doc.add_paragraph()
                source_para.add_run("Source: ").bold = True
                source_para.add_run(f"{equation['filename']}, Page {equation['page_num'] + 1}")
                source_para.style.font.size = Pt(10)
            
            # Add image if available and enabled
            if self.export_settings.include_images and equation.get('image_data'):
                self._add_equation_image(doc, equation['image_data'])
            
            # Add LaTeX if available and enabled
            if self.export_settings.include_latex and equation.get('latex_text'):
                self._add_latex_equation(doc, equation['latex_text'])
            
            # Add OCR text
            if equation.get('ocr_text'):
                ocr_para = doc.add_paragraph()
                ocr_para.add_run("OCR Text: ").bold = True
                ocr_para.add_run(equation['ocr_text'])
            
            # Add confidence and processing info if enabled
            if self.export_settings.include_confidence:
                self._add_processing_info(doc, equation)
            
            # Add spacing between equations
            doc.add_paragraph()
            
        except Exception as e:
            self.logger.error(f"Failed to add equation section: {e}")
    
    def _add_equation_image(self, doc: Document, image_data):
        """Add equation image to document"""
        try:
            # If image_data is a PIL Image, convert to bytes
            if hasattr(image_data, 'save'):
                img_buffer = io.BytesIO()
                image_data.save(img_buffer, format='PNG')
                img_buffer.seek(0)
                
                # Add image to document
                paragraph = doc.add_paragraph()
                paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
                run = paragraph.runs[0] if paragraph.runs else paragraph.add_run()
                run.add_picture(img_buffer, width=Inches(4))
                
            elif isinstance(image_data, (bytes, bytearray)):
                # Image data is already in bytes
                img_buffer = io.BytesIO(image_data)
                paragraph = doc.add_paragraph()
                paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER
                run = paragraph.runs[0] if paragraph.runs else paragraph.add_run()
                run.add_picture(img_buffer, width=Inches(4))
                
        except Exception as e:
            self.logger.warning(f"Could not add equation image: {e}")
    
    def _add_latex_equation(self, doc: Document, latex_text: str):
        """Add LaTeX equation to document"""
        try:
            # Add LaTeX as formatted text
            latex_para = doc.add_paragraph()
            latex_para.style = 'Equation'
            latex_para.add_run(latex_text)
            
            # Add caption
            caption_para = doc.add_paragraph("LaTeX Representation")
            caption_para.style = 'Equation Caption'
            
        except Exception as e:
            self.logger.warning(f"Could not add LaTeX equation: {e}")
    
    def _add_processing_info(self, doc: Document, equation: Dict[str, Any]):
        """Add processing information to document"""
        try:
            info_para = doc.add_paragraph()
            info_para.style.font.size = Pt(9)
            info_para.style.font.color.rgb = None  # Gray color
            
            info_text = []
            
            if equation.get('confidence') is not None:
                info_text.append(f"Confidence: {equation['confidence']:.1f}%")
            
            if equation.get('engine_used'):
                info_text.append(f"Engine: {equation['engine_used']}")
            
            if equation.get('processing_time') is not None:
                info_text.append(f"Processing Time: {equation['processing_time']:.2f}s")
            
            if info_text:
                info_para.add_run("Processing Info: ").bold = True
                info_para.add_run(" | ".join(info_text))
                
        except Exception as e:
            self.logger.warning(f"Could not add processing info: {e}")
    
    def export_single_equation(self, equation: Dict[str, Any], output_path: str) -> bool:
        """Export a single equation to Word document"""
        return self.export_equations([equation], output_path)
    
    def get_supported_formats(self) -> List[str]:
        """Get list of supported export formats"""
        return ['.docx']
    
    def validate_output_path(self, output_path: str) -> bool:
        """Validate if the output path is valid for Word export"""
        try:
            # Check if directory exists
            directory = os.path.dirname(output_path)
            if directory and not os.path.exists(directory):
                return False
            
            # Check file extension
            _, ext = os.path.splitext(output_path)
            if ext.lower() not in self.get_supported_formats():
                return False
            
            return True
            
        except Exception:
            return False
