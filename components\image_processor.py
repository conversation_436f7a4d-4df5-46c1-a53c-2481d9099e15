#!/usr/bin/env python3
"""
Image Processor Module for MathCapture Studio
Handles image processing operations for mathematical equation extraction
"""

import cv2
import numpy as np
from PIL import Image, ImageEnhance, ImageFilter
import logging
from typing import Tuple, List, Dict, Any, Optional
from dataclasses import dataclass
import os

@dataclass
class ProcessingSettings:
    """Settings for image processing"""
    enhance_contrast: bool = True
    denoise: bool = True
    sharpen: bool = True
    binarize: bool = True
    resize_factor: float = 1.0
    dpi_target: int = 300
    background_removal: bool = True

class ImageProcessor:
    """Handles image processing operations for OCR optimization"""
    
    def __init__(self, settings: Dict[str, Any] = None):
        self.settings = settings or {}
        self.logger = logging.getLogger(__name__)
        
        # Default processing settings
        self.processing_settings = ProcessingSettings()
        
    def preprocess_for_ocr(self, image: Image.Image, 
                          processing_settings: ProcessingSettings = None) -> Image.Image:
        """
        Preprocess image for optimal OCR results
        
        Args:
            image: PIL Image to process
            processing_settings: Processing configuration
            
        Returns:
            PIL Image: Processed image optimized for OCR
        """
        try:
            if processing_settings:
                self.processing_settings = processing_settings
            
            processed_img = image.copy()
            
            # Convert to RGB if necessary
            if processed_img.mode != 'RGB':
                processed_img = processed_img.convert('RGB')
            
            # Resize if needed
            if self.processing_settings.resize_factor != 1.0:
                processed_img = self._resize_image(processed_img)
            
            # Enhance contrast
            if self.processing_settings.enhance_contrast:
                processed_img = self._enhance_contrast(processed_img)
            
            # Remove background noise
            if self.processing_settings.background_removal:
                processed_img = self._remove_background(processed_img)
            
            # Denoise
            if self.processing_settings.denoise:
                processed_img = self._denoise_image(processed_img)
            
            # Sharpen
            if self.processing_settings.sharpen:
                processed_img = self._sharpen_image(processed_img)
            
            # Binarize for better OCR
            if self.processing_settings.binarize:
                processed_img = self._binarize_image(processed_img)
            
            self.logger.debug("Image preprocessing completed successfully")
            return processed_img
            
        except Exception as e:
            self.logger.error(f"Image preprocessing failed: {e}")
            return image  # Return original image on failure
    
    def _resize_image(self, image: Image.Image) -> Image.Image:
        """Resize image based on settings"""
        try:
            current_size = image.size
            new_size = (
                int(current_size[0] * self.processing_settings.resize_factor),
                int(current_size[1] * self.processing_settings.resize_factor)
            )
            
            # Use high-quality resampling
            return image.resize(new_size, Image.Resampling.LANCZOS)
            
        except Exception as e:
            self.logger.warning(f"Image resize failed: {e}")
            return image
    
    def _enhance_contrast(self, image: Image.Image) -> Image.Image:
        """Enhance image contrast for better OCR"""
        try:
            # Convert to OpenCV format
            cv_image = cv2.cvtColor(np.array(image), cv2.COLOR_RGB2BGR)
            
            # Apply CLAHE (Contrast Limited Adaptive Histogram Equalization)
            lab = cv2.cvtColor(cv_image, cv2.COLOR_BGR2LAB)
            l_channel, a, b = cv2.split(lab)
            
            clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
            l_channel = clahe.apply(l_channel)
            
            lab = cv2.merge((l_channel, a, b))
            enhanced = cv2.cvtColor(lab, cv2.COLOR_LAB2BGR)
            
            # Convert back to PIL
            return Image.fromarray(cv2.cvtColor(enhanced, cv2.COLOR_BGR2RGB))
            
        except Exception as e:
            self.logger.warning(f"Contrast enhancement failed: {e}")
            # Fallback to PIL enhancement
            try:
                enhancer = ImageEnhance.Contrast(image)
                return enhancer.enhance(1.5)
            except:
                return image
    
    def _remove_background(self, image: Image.Image) -> Image.Image:
        """Remove background noise and artifacts"""
        try:
            # Convert to OpenCV format
            cv_image = cv2.cvtColor(np.array(image), cv2.COLOR_RGB2BGR)
            
            # Convert to grayscale
            gray = cv2.cvtColor(cv_image, cv2.COLOR_BGR2GRAY)
            
            # Apply morphological operations to remove noise
            kernel = np.ones((3, 3), np.uint8)
            
            # Opening (erosion followed by dilation) to remove noise
            opening = cv2.morphologyEx(gray, cv2.MORPH_OPEN, kernel, iterations=1)
            
            # Closing (dilation followed by erosion) to fill gaps
            closing = cv2.morphologyEx(opening, cv2.MORPH_CLOSE, kernel, iterations=1)
            
            # Convert back to RGB
            result = cv2.cvtColor(closing, cv2.COLOR_GRAY2RGB)
            return Image.fromarray(result)
            
        except Exception as e:
            self.logger.warning(f"Background removal failed: {e}")
            return image
    
    def _denoise_image(self, image: Image.Image) -> Image.Image:
        """Remove noise from image"""
        try:
            # Convert to OpenCV format
            cv_image = cv2.cvtColor(np.array(image), cv2.COLOR_RGB2BGR)
            
            # Apply Non-local Means Denoising
            denoised = cv2.fastNlMeansDenoisingColored(cv_image, None, 10, 10, 7, 21)
            
            # Convert back to PIL
            return Image.fromarray(cv2.cvtColor(denoised, cv2.COLOR_BGR2RGB))
            
        except Exception as e:
            self.logger.warning(f"Denoising failed: {e}")
            # Fallback to PIL filter
            try:
                return image.filter(ImageFilter.MedianFilter(size=3))
            except:
                return image
    
    def _sharpen_image(self, image: Image.Image) -> Image.Image:
        """Sharpen image for better text recognition"""
        try:
            # Convert to OpenCV format
            cv_image = cv2.cvtColor(np.array(image), cv2.COLOR_RGB2BGR)
            
            # Create sharpening kernel
            kernel = np.array([[-1, -1, -1],
                              [-1,  9, -1],
                              [-1, -1, -1]])
            
            # Apply sharpening
            sharpened = cv2.filter2D(cv_image, -1, kernel)
            
            # Convert back to PIL
            return Image.fromarray(cv2.cvtColor(sharpened, cv2.COLOR_BGR2RGB))
            
        except Exception as e:
            self.logger.warning(f"Sharpening failed: {e}")
            # Fallback to PIL enhancement
            try:
                enhancer = ImageEnhance.Sharpness(image)
                return enhancer.enhance(1.5)
            except:
                return image
    
    def _binarize_image(self, image: Image.Image) -> Image.Image:
        """Convert image to binary (black and white) for OCR"""
        try:
            # Convert to OpenCV format
            cv_image = cv2.cvtColor(np.array(image), cv2.COLOR_RGB2BGR)
            gray = cv2.cvtColor(cv_image, cv2.COLOR_BGR2GRAY)
            
            # Apply adaptive thresholding
            binary = cv2.adaptiveThreshold(
                gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, 
                cv2.THRESH_BINARY, 11, 2
            )
            
            # Convert back to RGB
            result = cv2.cvtColor(binary, cv2.COLOR_GRAY2RGB)
            return Image.fromarray(result)
            
        except Exception as e:
            self.logger.warning(f"Binarization failed: {e}")
            # Fallback to simple threshold
            try:
                gray_image = image.convert('L')
                return gray_image.point(lambda x: 0 if x < 128 else 255, '1').convert('RGB')
            except:
                return image
    
    def detect_text_regions(self, image: Image.Image) -> List[Tuple[int, int, int, int]]:
        """
        Detect text regions in the image
        
        Args:
            image: PIL Image to analyze
            
        Returns:
            List of tuples (x, y, width, height) representing text regions
        """
        try:
            # Convert to OpenCV format
            cv_image = cv2.cvtColor(np.array(image), cv2.COLOR_RGB2BGR)
            gray = cv2.cvtColor(cv_image, cv2.COLOR_BGR2GRAY)
            
            # Apply morphological operations to find text regions
            kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (18, 18))
            connected = cv2.morphologyEx(gray, cv2.MORPH_CLOSE, kernel)
            
            # Find contours
            contours, _ = cv2.findContours(connected, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            # Filter and convert contours to bounding boxes
            text_regions = []
            for contour in contours:
                x, y, w, h = cv2.boundingRect(contour)
                
                # Filter by size (remove very small regions)
                if w > 20 and h > 10:
                    text_regions.append((x, y, w, h))
            
            return text_regions
            
        except Exception as e:
            self.logger.error(f"Text region detection failed: {e}")
            return []
    
    def crop_region(self, image: Image.Image, x: int, y: int, width: int, height: int) -> Image.Image:
        """
        Crop a specific region from the image
        
        Args:
            image: PIL Image to crop
            x, y: Top-left coordinates
            width, height: Dimensions of the region
            
        Returns:
            PIL Image: Cropped region
        """
        try:
            return image.crop((x, y, x + width, y + height))
        except Exception as e:
            self.logger.error(f"Image cropping failed: {e}")
            return image
    
    def get_image_info(self, image: Image.Image) -> Dict[str, Any]:
        """
        Get information about the image
        
        Args:
            image: PIL Image to analyze
            
        Returns:
            Dictionary with image information
        """
        try:
            info = {
                'size': image.size,
                'mode': image.mode,
                'format': getattr(image, 'format', 'Unknown'),
                'has_transparency': image.mode in ('RGBA', 'LA') or 'transparency' in image.info
            }
            
            # Calculate DPI if available
            if hasattr(image, 'info') and 'dpi' in image.info:
                info['dpi'] = image.info['dpi']
            else:
                info['dpi'] = (72, 72)  # Default DPI
            
            return info
            
        except Exception as e:
            self.logger.error(f"Failed to get image info: {e}")
            return {}
    
    def save_processed_image(self, image: Image.Image, output_path: str, quality: int = 95) -> bool:
        """
        Save processed image to file
        
        Args:
            image: PIL Image to save
            output_path: Path to save the image
            quality: JPEG quality (if applicable)
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Ensure directory exists
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            
            # Save with appropriate settings
            if output_path.lower().endswith('.jpg') or output_path.lower().endswith('.jpeg'):
                image.save(output_path, 'JPEG', quality=quality, optimize=True)
            else:
                image.save(output_path, optimize=True)
            
            self.logger.info(f"Image saved to {output_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to save image: {e}")
            return False
