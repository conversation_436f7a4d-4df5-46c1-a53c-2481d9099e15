#!/usr/bin/env python3
"""Test OCR functionality"""

print("Testing OCR functionality...")

try:
    from components.ocr_processor import MathOCRProcessor
    from components.settings_manager import SettingsManager
    from PIL import Image, ImageDraw
    
    # Initialize OCR processor
    sm = SettingsManager()
    settings = sm.load_settings()
    ocr = MathOCRProcessor(settings)
    
    # Create test image with text
    img = Image.new('RGB', (200, 100), 'white')
    draw = ImageDraw.Draw(img)
    draw.text((10, 30), 'x = 2 + 3', fill='black')
    
    # Test OCR
    text, conf = ocr.extract_text(img)
    
    print(f"✅ OCR Test Result: '{text.strip()}' (Confidence: {conf:.1f}%)")
    print("✅ OCR functionality is working!")
    
except Exception as e:
    print(f"❌ OCR test failed: {e}")
