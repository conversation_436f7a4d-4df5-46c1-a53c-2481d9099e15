"""
Enhanced Settings Manager with Donut Integration Support
MathCapture Studio - Responsive Edition v2.0.0

Manages application settings including OCR engine preferences,
performance monitoring, and hybrid processing configurations.
"""

import json
import os
import logging
from typing import Dict, Any, Optional

class SettingsManager:
    """Enhanced settings manager with Donut and performance monitoring support"""
    
    def __init__(self, config_dir: Optional[str] = None):
        self.logger = logging.getLogger(__name__)
        
        # Determine config directory
        if config_dir is None:
            self.config_dir = os.path.join(
                os.path.expanduser("~"), 
                ".mathcapture_studio"
            )
        else:
            self.config_dir = config_dir
            
        # Ensure config directory exists
        os.makedirs(self.config_dir, exist_ok=True)
        
        self.settings_file = os.path.join(self.config_dir, "settings.json")
        self.default_settings = self._get_default_settings()
        
    def _get_default_settings(self) -> Dict[str, Any]:
        """Get default application settings with Donut integration"""
        return {
            # Application settings
            "version": "2.0.0",
            "first_run": True,
            "auto_save": True,
            "auto_save_interval": 300,  # seconds
            
            # UI settings
            "theme": "modern",
            "font_family": "Segoe UI",
            "font_size": 10,
            "ui_scale": 1.0,
            "remember_window_state": True,
            "window_width": 1400,
            "window_height": 900,
            "window_x": 100,
            "window_y": 100,
            "window_maximized": False,
            
            # Layout settings
            "sidebar_width": 300,
            "editor_width": 350,
            "queue_height": 200,
            "sidebar_visible": True,
            "queue_visible": True,
            "layout_mode": "desktop",
            
            # ===== OCR ENGINE SETTINGS =====
            "ocr_engine": "auto",  # tesseract, donut, hybrid, auto
            "enable_donut": True,
            "donut_fallback_enabled": True,
            "hybrid_mode_enabled": True,
            
            # Tesseract settings
            "tesseract_path": "",
            "tesseract_psm": 6,
            "tesseract_oem": 3,
            "ocr_confidence_threshold": 60,
            "ocr_language": "eng",
            "math_detection_enabled": True,
            
            # Donut settings
            "donut_model": "naver-clova-ix/donut-base-finetuned-cord-v2",
            "donut_max_length": 512,
            "donut_num_beams": 1,
            "donut_max_image_size": 1024,
            "use_gpu": True,
            "gpu_memory_limit": 4096,  # MB
            
            # Hybrid processing settings
            "auto_engine_selection": True,
            "hybrid_confidence_threshold": 70,
            "donut_complexity_threshold": 60.0,
            "hybrid_complexity_threshold": 40.0,
            "complexity_analysis_enabled": True,
            
            # Performance settings
            "enable_result_cache": True,
            "max_cache_size": 100,
            "performance_monitoring_enabled": True,
            "max_performance_events": 1000,
            "auto_save_performance_data": True,
            "performance_save_interval": 300,
            
            # Processing thresholds
            "slow_processing_threshold": 10.0,  # seconds
            "low_confidence_threshold": 50.0,
            "high_confidence_threshold": 80.0,
            
            # Image processing settings
            "image_enhancement": "medium",  # none, low, medium, high
            "auto_crop": True,
            "skew_correction": True,
            "noise_reduction": True,
            "contrast_enhancement": "clahe",
            
            # Export settings
            "export_dir": os.path.expanduser("~/Documents"),
            "export_format": "docx",
            "inline_equations": False,
            "show_page_refs": True,
            "show_confidence": True,
            "show_metadata": True,
            "word_font_family": "Times New Roman",
            "word_font_size": 12,
            "include_performance_stats": False,
            
            # Preview settings
            "live_preview": True,
            "preview_engine": "mathjax",
            "preview_update_delay": 500,  # milliseconds
            
            # File handling settings
            "max_file_size": 100,  # MB
            "supported_formats": ["pdf", "png", "jpg", "jpeg", "bmp", "tiff"],
            "recent_files": [],
            "max_recent_files": 10,
            
            # Performance optimization settings
            "max_zoom": 5.0,
            "min_zoom": 0.1,
            "zoom_step": 1.25,
            "canvas_buffer_size": 2048,
            "thread_pool_size": 4,
            "batch_processing_size": 10,
            
            # Debug and logging settings
            "debug_mode": False,
            "log_level": "INFO",
            "save_debug_images": False,
            "debug_output_dir": os.path.join(os.path.expanduser("~"), "mathcapture_debug"),
            "enable_ocr_timing": True,
            "enable_complexity_logging": False,
            
            # Responsive design settings
            "responsive_breakpoints": {
                "mobile": 1000,
                "tablet": 1300,
                "desktop": 1300
            },
            "adaptive_ui": True,
            "touch_mode": False,
            "high_dpi_scaling": True,
            
            # ===== ADVANCED DONUT SETTINGS =====
            "donut_advanced": {
                "model_cache_dir": os.path.join(os.path.expanduser("~"), ".cache", "huggingface"),
                "force_cpu": False,
                "mixed_precision": True,
                "compile_model": False,  # PyTorch 2.0+ optimization
                "attention_implementation": "eager",  # eager, sdpa, flash_attention_2
                "torch_dtype": "float32",  # float32, float16, bfloat16
                "device_map": "auto",
                "low_cpu_mem_usage": True,
                "trust_remote_code": False,
            },
            
            # ===== ENGINE SELECTION RULES =====
            "engine_selection_rules": {
                "simple_text_threshold": 20.0,  # Use Tesseract for simple text
                "complex_math_threshold": 70.0,  # Use Donut for complex math
                "image_size_threshold": 500000,  # Pixel count threshold
                "processing_time_limit": 30.0,  # Max processing time per image
                "confidence_retry_threshold": 40.0,  # Retry with different engine
                "enable_smart_fallback": True,
            },
            
            # ===== PERFORMANCE TUNING =====
            "performance_tuning": {
                "tesseract_timeout": 30.0,  # seconds
                "donut_timeout": 60.0,  # seconds
                "max_concurrent_processes": 2,
                "memory_cleanup_interval": 100,  # operations
                "model_unload_timeout": 1800,  # seconds (30 minutes)
                "enable_memory_monitoring": True,
                "memory_warning_threshold": 80,  # percent
            },
            
            # ===== QUALITY CONTROL =====
            "quality_control": {
                "enable_result_validation": True,
                "min_text_length": 1,
                "max_text_length": 1000,
                "confidence_weighting": {
                    "tesseract": 1.0,
                    "donut": 1.1,  # Slight preference for Donut
                    "hybrid": 1.2   # Preference for hybrid results
                },
                "enable_post_processing": True,
                "latex_validation": True,
            },
            
            # ===== EXPERIMENTAL FEATURES =====
            "experimental": {
                "enable_batch_optimization": False,
                "enable_model_quantization": False,
                "enable_onnx_runtime": False,
                "enable_tensorrt": False,
                "enable_openvino": False,
                "auto_model_selection": False,
            }
        }
        
    def load_settings(self) -> Dict[str, Any]:
        """Load settings from file or return defaults"""
        try:
            if os.path.exists(self.settings_file):
                with open(self.settings_file, 'r', encoding='utf-8') as f:
                    saved_settings = json.load(f)
                    
                # Merge with defaults to ensure all keys exist
                settings = self.default_settings.copy()
                self._deep_update(settings, saved_settings)
                
                # Validate settings
                settings = self._validate_settings(settings)
                
                self.logger.info("Settings loaded successfully")
                return settings
            else:
                self.logger.info("No settings file found, using defaults")
                return self.default_settings.copy()
                
        except Exception as e:
            self.logger.error(f"Failed to load settings: {e}")
            return self.default_settings.copy()
    
    def _deep_update(self, base_dict: Dict, update_dict: Dict):
        """Deep update dictionary (recursive merge)"""
        for key, value in update_dict.items():
            if key in base_dict and isinstance(base_dict[key], dict) and isinstance(value, dict):
                self._deep_update(base_dict[key], value)
            else:
                base_dict[key] = value
                
    def save_settings(self, settings: Dict[str, Any]) -> bool:
        """Save settings to file"""
        try:
            # Validate before saving
            validated_settings = self._validate_settings(settings)
            
            with open(self.settings_file, 'w', encoding='utf-8') as f:
                json.dump(validated_settings, f, indent=2, ensure_ascii=False)
                
            self.logger.info("Settings saved successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to save settings: {e}")
            return False
            
    def _validate_settings(self, settings: Dict[str, Any]) -> Dict[str, Any]:
        """Validate and sanitize settings"""
        validated = settings.copy()
        
        # Validate OCR engine selection
        valid_engines = ["tesseract", "donut", "hybrid", "auto"]
        if validated.get("ocr_engine") not in valid_engines:
            validated["ocr_engine"] = "auto"
        
        # Validate numeric ranges
        validated["ui_scale"] = max(0.5, min(3.0, validated.get("ui_scale", 1.0)))
        validated["font_size"] = max(8, min(24, validated.get("font_size", 10)))
        validated["word_font_size"] = max(8, min(72, validated.get("word_font_size", 12)))
        validated["ocr_confidence_threshold"] = max(0, min(100, validated.get("ocr_confidence_threshold", 60)))
        validated["hybrid_confidence_threshold"] = max(0, min(100, validated.get("hybrid_confidence_threshold", 70)))
        validated["max_zoom"] = max(1.0, min(10.0, validated.get("max_zoom", 5.0)))
        validated["min_zoom"] = max(0.05, min(1.0, validated.get("min_zoom", 0.1)))
        
        # Validate Donut settings
        validated["donut_max_length"] = max(64, min(2048, validated.get("donut_max_length", 512)))
        validated["donut_num_beams"] = max(1, min(8, validated.get("donut_num_beams", 1)))
        validated["donut_max_image_size"] = max(256, min(2048, validated.get("donut_max_image_size", 1024)))
        validated["gpu_memory_limit"] = max(1024, min(32768, validated.get("gpu_memory_limit", 4096)))
        
        # Validate complexity thresholds
        validated["donut_complexity_threshold"] = max(0, min(100, validated.get("donut_complexity_threshold", 60.0)))
        validated["hybrid_complexity_threshold"] = max(0, min(100, validated.get("hybrid_complexity_threshold", 40.0)))
        
        # Validate performance settings
        validated["max_cache_size"] = max(10, min(1000, validated.get("max_cache_size", 100)))
        validated["max_performance_events"] = max(100, min(10000, validated.get("max_performance_events", 1000)))
        validated["thread_pool_size"] = max(1, min(16, validated.get("thread_pool_size", 4)))
        
        # Validate timeout settings
        if "performance_tuning" in validated:
            perf_settings = validated["performance_tuning"]
            perf_settings["tesseract_timeout"] = max(5.0, min(120.0, perf_settings.get("tesseract_timeout", 30.0)))
            perf_settings["donut_timeout"] = max(10.0, min(300.0, perf_settings.get("donut_timeout", 60.0)))
            perf_settings["max_concurrent_processes"] = max(1, min(8, perf_settings.get("max_concurrent_processes", 2)))
        
        # Validate paths
        export_dir = validated.get("export_dir", "")
        if not os.path.exists(export_dir):
            validated["export_dir"] = os.path.expanduser("~/Documents")
            
        # Validate window dimensions
        validated["window_width"] = max(800, min(3840, validated.get("window_width", 1400)))
        validated["window_height"] = max(600, min(2160, validated.get("window_height", 900)))
        
        # Validate panel sizes
        validated["sidebar_width"] = max(200, min(600, validated.get("sidebar_width", 300)))
        validated["editor_width"] = max(250, min(800, validated.get("editor_width", 350)))
        validated["queue_height"] = max(100, min(500, validated.get("queue_height", 200)))
        
        # Validate image enhancement setting
        valid_enhancements = ["none", "low", "medium", "high"]
        if validated.get("image_enhancement") not in valid_enhancements:
            validated["image_enhancement"] = "medium"
        
        # Validate log level
        valid_log_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        if validated.get("log_level") not in valid_log_levels:
            validated["log_level"] = "INFO"
        
        return validated
        
    def get_setting(self, key: str, default: Any = None) -> Any:
        """Get a specific setting value with dot notation support"""
        settings = self.load_settings()
        
        # Support dot notation for nested settings
        if '.' in key:
            keys = key.split('.')
            value = settings
            for k in keys:
                if isinstance(value, dict) and k in value:
                    value = value[k]
                else:
                    return default
            return value
        else:
            return settings.get(key, default)
        
    def set_setting(self, key: str, value: Any) -> bool:
        """Set a specific setting value with dot notation support"""
        settings = self.load_settings()
        
        # Support dot notation for nested settings
        if '.' in key:
            keys = key.split('.')
            current = settings
            for k in keys[:-1]:
                if k not in current:
                    current[k] = {}
                current = current[k]
            current[keys[-1]] = value
        else:
            settings[key] = value
            
        return self.save_settings(settings)
        
    def reset_to_defaults(self) -> bool:
        """Reset all settings to defaults"""
        return self.save_settings(self.default_settings.copy())
        
    def reset_ocr_settings(self) -> bool:
        """Reset only OCR-related settings to defaults"""
        settings = self.load_settings()
        defaults = self.default_settings
        
        # OCR engine settings
        ocr_keys = [
            "ocr_engine", "enable_donut", "donut_fallback_enabled", "hybrid_mode_enabled",
            "tesseract_path", "tesseract_psm", "tesseract_oem", "ocr_confidence_threshold",
            "ocr_language", "math_detection_enabled", "donut_model", "donut_max_length",
            "donut_num_beams", "donut_max_image_size", "use_gpu", "gpu_memory_limit",
            "auto_engine_selection", "hybrid_confidence_threshold", "donut_complexity_threshold",
            "hybrid_complexity_threshold", "complexity_analysis_enabled"
        ]
        
        for key in ocr_keys:
            if key in defaults:
                settings[key] = defaults[key]
        
        # Reset nested OCR settings
        if "donut_advanced" in defaults:
            settings["donut_advanced"] = defaults["donut_advanced"].copy()
        if "engine_selection_rules" in defaults:
            settings["engine_selection_rules"] = defaults["engine_selection_rules"].copy()
        if "performance_tuning" in defaults:
            settings["performance_tuning"] = defaults["performance_tuning"].copy()
        
        return self.save_settings(settings)
        
    def export_settings(self, filepath: str) -> bool:
        """Export settings to a file"""
        try:
            settings = self.load_settings()
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(settings, f, indent=2, ensure_ascii=False)
            return True
        except Exception as e:
            self.logger.error(f"Failed to export settings: {e}")
            return False
            
    def import_settings(self, filepath: str) -> bool:
        """Import settings from a file"""
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                imported_settings = json.load(f)
            return self.save_settings(imported_settings)
        except Exception as e:
            self.logger.error(f"Failed to import settings: {e}")
            return False
    
    def get_ocr_engine_config(self) -> Dict[str, Any]:
        """Get OCR engine configuration"""
        settings = self.load_settings()
        
        return {
            "primary_engine": settings.get("ocr_engine", "auto"),
            "donut_enabled": settings.get("enable_donut", True),
            "hybrid_enabled": settings.get("hybrid_mode_enabled", True),
            "auto_selection": settings.get("auto_engine_selection", True),
            "complexity_analysis": settings.get("complexity_analysis_enabled", True),
            "thresholds": {
                "confidence": settings.get("hybrid_confidence_threshold", 70),
                "complexity_donut": settings.get("donut_complexity_threshold", 60.0),
                "complexity_hybrid": settings.get("hybrid_complexity_threshold", 40.0)
            },
            "performance": settings.get("performance_tuning", {}),
            "quality_control": settings.get("quality_control", {})
        }
    
    def update_performance_settings(self, performance_data: Dict[str, Any]) -> bool:
        """Update performance settings based on monitoring data"""
        try:
            settings = self.load_settings()
            
            # Auto-adjust thresholds based on performance
            if "recommendations" in performance_data:
                for rec in performance_data["recommendations"]:
                    if rec["type"] == "engine_performance" and rec["priority"] == "high":
                        # Increase Donut usage threshold
                        current_threshold = settings.get("donut_complexity_threshold", 60.0)
                        settings["donut_complexity_threshold"] = max(40.0, current_threshold - 10.0)
                    
                    elif rec["type"] == "performance" and "slow" in rec["message"].lower():
                        # Increase Tesseract preference for speed
                        current_threshold = settings.get("hybrid_confidence_threshold", 70.0)
                        settings["hybrid_confidence_threshold"] = min(90.0, current_threshold + 5.0)
            
            return self.save_settings(settings)
            
        except Exception as e:
            self.logger.error(f"Failed to update performance settings: {e}")
            return False
