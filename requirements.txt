# MathCapture Studio - Responsive Edition with Donut Integration
# Core dependencies for mathematical equation extraction and Word export

# GUI Framework
# tkinter is included with Python, no need to install

# Image Processing
Pillow>=10.0.0
opencv-python>=4.8.0
numpy>=1.24.0

# PDF Processing
PyMuPDF>=1.23.0
pdf2image>=3.1.0

# Traditional OCR Engine
pytesseract>=0.3.10

# Word Document Generation
python-docx>=0.8.11
lxml>=4.9.0

# Additional Utilities
dataclasses>=0.6  # For Python < 3.7
typing-extensions>=4.7.0

# ===== DONUT (AI-POWERED OCR) DEPENDENCIES =====
# Install these for enhanced AI-powered mathematical equation recognition
# Note: These are large packages and require significant disk space and memory

# Deep Learning Framework (choose one)
torch>=2.0.0  # CPU version - for GPU: torch>=2.0.0+cu118
torchvision>=0.15.0

# Transformers and NLP
transformers>=4.30.0
tokenizers>=0.13.0
sentencepiece>=0.1.99

# Additional ML dependencies
accelerate>=0.20.0  # For faster model loading
datasets>=2.12.0    # For data handling (optional)

# ===== OPTIONAL PERFORMANCE ENHANCEMENTS =====
# Uncomment these for better performance

# GPU acceleration (if you have NVIDIA GPU)
# torch>=2.0.0+cu118 --index-url https://download.pytorch.org/whl/cu118
# torchvision>=0.15.0+cu118 --index-url https://download.pytorch.org/whl/cu118

# Faster image processing
# opencv-contrib-python>=4.8.0  # Additional OpenCV modules

# Enhanced UI components
# tkinter-tooltip>=2.0.0
# ttkthemes>=3.2.2

# Scientific computing optimizations
# numba>=0.57.0
# scipy>=1.11.0

# ===== DEVELOPMENT DEPENDENCIES (OPTIONAL) =====
# Uncomment for development

# Testing
# pytest>=7.4.0
# pytest-cov>=4.1.0

# Code formatting and linting
# black>=23.0.0
# flake8>=6.0.0
# mypy>=1.4.0

# Documentation
# sphinx>=7.0.0
# sphinx-rtd-theme>=1.2.0

# ===== INSTALLATION NOTES =====
# 
# 1. BASIC INSTALLATION (Tesseract only):
#    pip install -r requirements.txt --no-deps
#    pip install Pillow opencv-python numpy PyMuPDF pdf2image pytesseract python-docx lxml
#
# 2. FULL INSTALLATION (with Donut AI):
#    pip install -r requirements.txt
#
# 3. GPU INSTALLATION (for faster Donut processing):
#    pip install torch torchvision --index-url https://download.pytorch.org/whl/cu118
#    pip install -r requirements.txt
#
# 4. TESSERACT OCR INSTALLATION:
#    Download from: https://github.com/UB-Mannheim/tesseract/wiki
#    Make sure tesseract.exe is in your PATH
#    Install math training data for better equation recognition
#
# 5. DISK SPACE REQUIREMENTS:
#    - Basic installation: ~200MB
#    - With Donut: ~2-4GB (includes PyTorch and models)
#    - With GPU support: ~4-6GB
#
# 6. MEMORY REQUIREMENTS:
#    - Tesseract only: 2-4GB RAM
#    - With Donut (CPU): 4-8GB RAM
#    - With Donut (GPU): 4-6GB RAM + 2-4GB VRAM
